import fs from 'fs'
import path from 'path'

export async function writeTranslationFiles(
  localeData: Record<string, Record<string, any>>
): Promise<void> {
  try {
    // Get the output directory from environment variable or use default
    const outputDir = process.env.I18N_OUTPUT_PATH || path.resolve(process.cwd(), 'public', 'locales')

    // Ensure the directory exists
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true })
    }

    // Write files for English and Estonian
    const locales = ['en', 'et']
    const writePromises = locales.map((locale) => {
      return new Promise<void>((resolve, reject) => {
        const filePath = path.join(outputDir, `${locale}.json`)
        const jsonContent = JSON.stringify(localeData[locale], null, 2)

        fs.writeFile(filePath, jsonContent, 'utf8', (error) => {
          if (error) {
            console.error(`Error writing ${locale}.json:`, error)
            reject(error)
          } else {
            console.log(`Successfully wrote ${filePath}`)
            resolve()
          }
        })
      })
    })

    await Promise.all(writePromises)

    // Create a manifest file with metadata
    const manifest = {
      locales: ['en', 'et'],
      generatedAt: new Date().toISOString(),
      totalKeys: {
        en: countKeys(localeData.en),
        et: countKeys(localeData.et),
      },
    }

    const manifestPath = path.join(outputDir, 'manifest.json')
    fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2))

  } catch (error) {
    console.error('Error in writeTranslationFiles:', error)
    throw error
  }
}

// Helper function to count nested keys
function countKeys(obj: Record<string, any>): number {
  let count = 0
  for (const value of Object.values(obj)) {
    if (typeof value === 'object' && value !== null) {
      count += countKeys(value)
    } else {
      count++
    }
  }
  return count
}
