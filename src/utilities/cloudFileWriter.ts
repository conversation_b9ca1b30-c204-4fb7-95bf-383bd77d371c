import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3'

const s3Client = new S3Client({
  region: process.env.AWS_REGION || 'us-east-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
})

export async function writeTranslationFilesToS3(
  localeData: Record<string, Record<string, any>>
): Promise<void> {
  const bucketName = process.env.S3_BUCKET_NAME!
  const keyPrefix = process.env.S3_KEY_PREFIX || 'locales/'

  try {
    const uploadPromises = Object.entries(localeData).map(async ([locale, translations]) => {
      const key = `${keyPrefix}${locale}.json`
      const body = JSON.stringify(translations, null, 2)

      const command = new PutObjectCommand({
        Bucket: bucketName,
        Key: key,
        Body: body,
        ContentType: 'application/json',
        CacheControl: 'public, max-age=300', // 5 minutes cache
      })

      return s3Client.send(command)
    })

    await Promise.all(uploadPromises)
    console.log(`Successfully uploaded translation files to S3`)
  } catch (error) {
    console.error('Error uploading to S3:', error)
    throw error
  }
}
