import { generateTranslationFiles } from '@/Translations/hooks/generateTranslationFiles'
import { GlobalConfig } from 'payload'

export const Translations: GlobalConfig = {
  slug: 'translations',
  label: {
    en: 'Translations',
    et: 'Tõlked',
  },
  admin: {
    group: 'Site Configuration',
    description: 'Manage application translations for English and Estonian',
  },
  access: {
    read: () => true,
    update: ({ req: { user } }) => <PERSON><PERSON>an(user),
  },
  fields: [
    {
      name: 'translations',
      type: 'array',
      label: 'Translations',
      admin: {
        components: {
          RowLabel: '@/Translations/RowLabel#RowLabel',
        },
      },
      fields: [
        {
          name: 'key',
          type: 'text',
          required: true,
          admin: {
            description: 'Translation key (e.g., common.submit, auth.login.title)',
            placeholder: 'common.submit',
          },
          validate: (value: string) => {
            if (!value) return 'Key is required'
            if (!/^[a-zA-Z0-9._-]+$/.test(value)) {
              return 'Key can only contain letters, numbers, dots, underscores, and hyphens'
            }
            return true
          },
        },
        {
          name: 'description',
          type: 'text',
          admin: {
            description: 'Optional context for translators',
            placeholder: 'Submit button for forms',
          },
        },
        {
          name: 'en',
          type: 'text',
          label: 'English',
          required: true,
          admin: {
            placeholder: 'English translation',
          },
        },
        {
          name: 'et',
          type: 'text',
          label: 'Estonian',
          required: true,
          admin: {
            placeholder: 'Estonian translation',
          },
        },
      ],
    },
  ],
  hooks: {
    afterChange: [generateTranslationFiles],
  },
  versions: {
    drafts: true,
  },
}
