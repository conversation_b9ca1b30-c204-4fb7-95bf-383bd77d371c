import { GlobalAfterChangeHook } from 'payload'
import { writeTranslationFiles } from '@/utilities/fileWriter'

export const generateTranslationFiles: GlobalAfterChangeHook = async ({
                                                                        req,
                                                                        doc,
                                                                        previousDoc,
                                                                      }) => {
  try {
    console.log('Translation global updated, regenerating translation files...')

    // Transform the global document into i18n file format
    const transformedData = transformGlobalToI18nFormat(doc)

    // Write the files to the filesystem
    await writeTranslationFiles(transformedData)

    console.log(`Successfully generated translation files for English and Estonian`)
  } catch (error) {
    console.error('Error generating translation files:', error)
    // Don't throw to avoid breaking the save operation
  }
}

// Transform global document to i18n file format
function transformGlobalToI18nFormat(doc: any): Record<string, Record<string, any>> {
  const localeData: Record<string, Record<string, any>> = {
    en: {},
    et: {},
  }

  // Process translations array
  if (doc.translations && Array.isArray(doc.translations)) {
    doc.translations.forEach((item: any) => {
      if (item.key && item.en && item.et) {
        setNestedValue(localeData.en, item.key, item.en)
        setNestedValue(localeData.et, item.key, item.et)
      }
    })
  }

  return localeData
}

// Helper function to set nested object values using dot notation
function setNestedValue(obj: any, path: string, value: any) {
  const keys = path.split('.')
  let current = obj

  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i]
    if (!current[key] || typeof current[key] !== 'object') {
      current[key] = {}
    }
    current = current[key]
  }

  current[keys[keys.length - 1]] = value
}
